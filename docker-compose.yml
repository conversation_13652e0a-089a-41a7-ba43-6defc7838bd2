services:
  cursor-bridge:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      # 可选：持久化日志
      - ./logs:/app/logs
    restart: unless-stopped
    # 共享内存大小，Chrome 需要
    shm_size: 2gb
    # 安全选项
    security_opt:
      - seccomp:unconfined
    # 容器名称
    container_name: cursor-bridge
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 网络配置
networks:
  default:
    name: cursor-bridge-network