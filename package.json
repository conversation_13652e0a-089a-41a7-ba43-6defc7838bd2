{"name": "cursor-bridge", "version": "1.0.0", "description": "OpenAI compatible API bridge for Cursor.com using browser automation", "main": "src/index.js", "scripts": {"start": "node src/index.js", "start:auto": "AUTO_BROWSER=true node src/index.js", "start:manual": "AUTO_BROWSER=false node src/index.js", "start:headless": "AUTO_BROWSER=true HEADLESS=true node src/index.js", "start:docker": "AUTO_BROWSER=true HEADLESS=true DEBUG=true node src/index.js", "dev": "nodemon src/index.js", "test": "node test/test.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^4.18.2", "puppeteer": "^21.6.1", "uuid": "^9.0.1"}, "devDependencies": {"axios": "^1.6.2", "nodemon": "^3.0.2"}, "keywords": ["openai", "cursor", "api", "bridge", "puppeteer"], "author": "", "license": "MIT"}