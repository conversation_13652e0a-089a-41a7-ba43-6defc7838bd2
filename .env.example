# ===== Cursor Bridge 配置文件 =====

# 服务器端口
PORT=8000

# 是否启用自动浏览器 (true/false)
# true: 自动启动浏览器并注入脚本
# false: 手动模式，需要用户手动注入
AUTO_BROWSER=true

# 是否以无头模式运行浏览器 (true/false)
# true: 无头模式，适用于服务器环境或Docker容器
# false: 可视模式，适用于本地开发和首次验证
HEADLESS=true

# 是否启用调试日志 (true/false)
# true: 显示详细的调试信息
# false: 只显示基本日志
DEBUG=true

# 浏览器选择 (edge/chrome/chromium/auto)
# edge: 优先使用 Microsoft Edge
# chrome: 优先使用 Google Chrome
# chromium: 优先使用 Chromium
# auto: 自动检测 (Edge > Chrome > Chromium)
BROWSER=auto

# ===== 使用场景示例 =====
# 本地开发 (推荐): AUTO_BROWSER=true, HEADLESS=false, DEBUG=true
# 服务器部署: AUTO_BROWSER=true, HEADLESS=true, DEBUG=false
# Docker 容器: AUTO_BROWSER=true, HEADLESS=true, DEBUG=true
# 手动模式: AUTO_BROWSER=false, HEADLESS=false, DEBUG=false

# ===== 快速配置 =====
# 复制本文件为 .env 并根据需要修改配置
# 或使用以下 npm 脚本:
# npm start          - 使用 .env 配置
# npm run start:auto - 自动模式 (可视)
# npm run start:manual - 手动模式
# npm run start:headless - 无头模式
# npm run start:docker - Docker 模式